import { NextResponse } from "next/server";
import { db } from "@/lib/db/config";
import { events } from "@/lib/db/schema";
import { CalendarEvent } from "@/types/events";

export async function GET() {
  try {
    // Fetch events from database
    const dbEvents = await db.select().from(events);

    // Convert to the format expected by the frontend
    const formattedEvents: CalendarEvent[] = dbEvents.map((event) => ({
      id: event.id,
      title: event.title,
      description: event.description || undefined,
      location: event.location || undefined,
      start: event.start,
      end: event.end,
      allDay: event.allDay || false,
      color: event.color || undefined,
      teamMemberId: event.teamMemberId || undefined,
    }));

    return NextResponse.json({ events: formattedEvents });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { error: "Failed to fetch events" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Insert new event into database
    const [newEvent] = await db
      .insert(events)
      .values({
        title: body.title,
        description: body.description,
        location: body.location,
        start: new Date(body.start),
        end: new Date(body.end),
        allDay: body.allDay || false,
        color: body.color,
        teamMemberId: body.teamMemberId,
      })
      .returning();

    // Convert to the format expected by the frontend
    const formattedEvent: CalendarEvent = {
      id: newEvent.id,
      title: newEvent.title,
      description: newEvent.description || undefined,
      location: newEvent.location || undefined,
      start: newEvent.start,
      end: newEvent.end,
      allDay: newEvent.allDay || false,
      color: newEvent.color || undefined,
      teamMemberId: newEvent.teamMemberId || undefined,
    };

    return NextResponse.json({ event: formattedEvent });
  } catch (error) {
    console.error("Error creating event:", error);
    return NextResponse.json(
      { error: "Failed to create event" },
      { status: 500 },
    );
  }
}
