import { NextResponse } from "next/server";
import { db } from "@/lib/db/config";
import { users, blockouts } from "@/lib/db/schema";
import { TeamMember } from "@/types/user";
import { eq } from "drizzle-orm";

/**
 *
 * @returns Returns a list of users
 */
export async function GET() {
  try {
    // Fetch users from database
    const dbUsers = await db.select().from(users);
    // fetch

    console.log("dbUsers", dbUsers);

    // Convert to the format expected by the frontend
    const formattedUsers: TeamMember[] = await Promise.all(
      dbUsers.map(async (user) => {
        // get blockouts for user
        const userBlockouts = await db
          .select()
          .from(blockouts)
          .where(eq(blockouts.userId, user.id));

        // Use the first blockout or create a default one
        const blockout = userBlockouts[0] || {
          id: 0,
          userId: user.id,
          startDate: new Date(),
          endDate: new Date(),
          reason: "No blockout",
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        return {
          id: user.id,
          name: user.name,
          role: user.role,
          status: user.status as "available" | "unavailable" | "tentative",
          blockout: blockout,
          email: user.email || undefined,
          phone: user.phone || undefined,
          avatar: user.avatar || undefined,
        };
      }),
    );

    return NextResponse.json({ users: formattedUsers });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Insert new user into database
    const [newUser] = await db
      .insert(users)
      .values({
        name: body.name,
        role: body.role,
        status: body.status || "available",
        email: body.email,
        phone: body.phone,
        avatar: body.avatar,
      })
      .returning();

    // Convert to the format expected by the frontend
    const formattedUser: TeamMember = {
      id: newUser.id,
      name: newUser.name,
      role: newUser.role,
      status: newUser.status as "available" | "unavailable" | "tentative",
      blockout: {
        id: 0,
        userId: newUser.id,
        startDate: new Date(),
        endDate: new Date(),
        reason: "No blockout",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      email: newUser.email || undefined,
      phone: newUser.phone || undefined,
      avatar: newUser.avatar || undefined,
    };

    return NextResponse.json({ user: formattedUser });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 },
    );
  }
}
