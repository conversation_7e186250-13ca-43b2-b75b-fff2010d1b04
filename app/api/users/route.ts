import { NextResponse } from "next/server";
import { db } from "@/lib/db/config";
import { users, blockouts } from "@/lib/db/schema";
import { TeamMember } from "@/types/user";
import { sql, eq } from "drizzle-orm";

/**
 *
 * @returns Returns a list of users
 */
export async function GET() {
  try {
    // Fetch users from database
    const dbUsers = await db.select().from(users);
    // fetch

    console.log("dbUsers", dbUsers);

    // Convert to the format expected by the frontend
    const formattedUsers: TeamMember[] = dbUsers.map(async (user) => {
      // get blockouts for user
      const userBlockouts = await db
        .select()
        .from(blockouts)
        .where(eq(blockouts.userId, user.id));

      return {
        id: user.id,
        name: user.name,
        role: user.role,
        status: user.status as "available" | "unavailable" | "tentative",
        blockout: "",
        // email: user.email || undefined,
        // phone: user.phone || undefined,
        // avatar: user.avatar || undefined,
      };
    });

    return NextResponse.json({ users: formattedUsers });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Insert new user into database
    const [newUser] = await db
      .insert(users)
      .values({
        name: body.name,
        role: body.role,
        status: body.status || "available",
        email: body.email,
        phone: body.phone,
        avatar: body.avatar,
      })
      .returning();

    // Convert to the format expected by the frontend
    const formattedUser: TeamMember = {
      id: newUser.id,
      name: newUser.name,
      role: newUser.role,
      status: newUser.status as "available" | "unavailable" | "tentative",
      blockout: "11/04 - 12/31", // TODO: Calculate from blockouts table
      email: newUser.email || undefined,
      phone: newUser.phone || undefined,
      avatar: newUser.avatar || undefined,
    };

    return NextResponse.json({ user: formattedUser });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 },
    );
  }
}
