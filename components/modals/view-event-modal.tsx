"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@heroui/modal";
import { But<PERSON> } from "@heroui/button";
import { CalendarEvent } from "@/types/events";
import { useUsers } from "@/hooks/useUsers";

interface ViewEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedEvent: CalendarEvent | null;
}

const ViewEventModal: React.FC<ViewEventModalProps> = ({
  isOpen,
  onClose,
  selectedEvent,
}) => {
  const { users, isLoading: usersLoading, isError: usersError } = useUsers();

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalContent>
        <ModalHeader>Event Details</ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            {selectedEvent && (
              <>
                {/* Event Title */}
                <div>
                  <p className="text-sm font-medium">Event Title</p>
                  <p>{selectedEvent.title}</p>
                </div>

                {/* Event Start Date */}
                <div>
                  <p className="text-sm font-medium">Start Time</p>
                  <p>
                    {new Date(selectedEvent.start).toLocaleString("en-US", {
                      timeZoneName: "short",
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                      hour: "numeric",
                      minute: "numeric",
                    })}
                  </p>
                </div>

                {/* Event End Date */}
                <div>
                  <p className="text-sm font-medium">End Time</p>
                  <p>
                    {new Date(selectedEvent.end).toLocaleString("en-US", {
                      timeZoneName: "short",
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                      hour: "numeric",
                      minute: "numeric",
                    })}
                  </p>
                </div>

                {/* Event Description */}
                {selectedEvent.description && (
                  <div>
                    <p className="text-sm font-medium">Description</p>
                    <p>{selectedEvent.description}</p>
                  </div>
                )}

                {/* Team Member Blockouts */}
                <div>
                  <p className="text-sm font-medium">Team Member Blockouts</p>
                  <div className="mt-2 max-h-40 overflow-y-auto rounded border border-gray-200 p-2 dark:border-gray-700">
                    {/* Filter team members blockout by event date */}
                    {users.map((user) => (
                      <div
                        key={user.id}
                        className={`mb-1 flex items-center justify-between border-b border-gray-100 pb-1 dark:border-gray-800 ${
                          selectedEvent.teamMemberId === user.id
                            ? "rounded bg-primary-100 px-1 dark:bg-primary-900/20"
                            : ""
                        }`}
                      >
                        <span className="font-medium">{user.name}</span>
                        <span className="text-sm text-gray-500">
                          {user.blockout}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="danger" variant="light" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ViewEventModal;
