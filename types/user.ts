import { Blockout } from "./blockout";

export type UserStatus = "available" | "unavailable" | "tentative";

export interface TeamMember {
  id: number;
  name: string;
  role: string;
  status: UserStatus;
  blockout: Blockout;
  email?: string;
  phone?: string;
  avatar?: string;
}

export interface TeamMembers {
  teamMembers: TeamMember[];
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  isLoading: boolean;
}
