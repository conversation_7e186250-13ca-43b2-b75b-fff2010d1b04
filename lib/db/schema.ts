import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
  text,
  integer,
  uuid,
} from "drizzle-orm/pg-core";

// Users/Team Members table
export const users = pgTable("users", {
  id: serial("id").primary<PERSON>ey(),
  name: varchar("name", { length: 255 }).notNull(),
  role: varchar("role", { length: 255 }).notNull(),
  status: varchar("status", { length: 50 }).notNull().default("available"), // available, unavailable, tentative
  email: varchar("email", { length: 255 }),
  phone: varchar("phone", { length: 50 }),
  avatar: varchar("avatar", { length: 500 }),
  blockout: varchar("blockout", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Events table
export const events = pgTable("events", {
  id: uuid("id").defaultRandom().primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  location: varchar("location", { length: 255 }),
  start: timestamp("start").notNull(),
  end: timestamp("end").notNull(),
  allDay: boolean("all_day").default(false),
  color: varchar("color", { length: 7 }), // hex color code
  teamMemberId: integer("team_member_id").array(),
  // .references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Blockout dates table
export const blockouts = pgTable("blockouts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id")
    .references(() => users.id)
    .notNull(),
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  reason: varchar("reason", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Event = typeof events.$inferSelect;
export type NewEvent = typeof events.$inferInsert;
export type Blockout = typeof blockouts.$inferSelect;
export type NewBlockout = typeof blockouts.$inferInsert;
